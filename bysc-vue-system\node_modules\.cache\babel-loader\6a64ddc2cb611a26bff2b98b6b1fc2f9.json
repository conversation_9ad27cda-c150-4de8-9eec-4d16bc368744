{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue", "mtime": 1753841933930}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["export default {\n  name: 'ChangeTenantDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    terminalInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    tenantList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      form: {\n        terminalId: '',\n        terminalName: '',\n        currentTenant: '',\n        tenantId: ''\n      },\n      rules: {\n        tenantId: [{\n          required: true,\n          message: '请选择租户',\n          trigger: 'change'\n        }]\n      },\n      confirmDialogVisible: false\n    };\n  },\n  computed: {\n    drawerVisible: {\n      get: function get() {\n        return this.visible;\n      },\n      set: function set(val) {\n        this.$emit('update:visible', val);\n      }\n    },\n    // 过滤掉当前租户，不能选择当前租户\n    availableTenants: function availableTenants() {\n      var _this = this;\n      return this.tenantList.filter(function (tenant) {\n        return tenant.id !== _this.terminalInfo.tenantId;\n      });\n    }\n  },\n  watch: {\n    visible: function visible(val) {\n      if (val) {\n        this.initForm();\n      } else {\n        this.confirmDialogVisible = false;\n      }\n    },\n    terminalInfo: {\n      handler: function handler(val) {\n        if (val && this.visible) {\n          this.initForm();\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initForm: function initForm() {\n      var _this2 = this;\n      this.form = {\n        terminalId: this.terminalInfo.terminalId || '',\n        terminalName: this.terminalInfo.terminalName || '',\n        currentTenant: this.terminalInfo.currentTenant || '',\n        tenantId: ''\n      };\n      // 清除验证\n      this.$nextTick(function () {\n        if (_this2.$refs.changeForm) {\n          _this2.$refs.changeForm.clearValidate();\n        }\n      });\n    },\n    handleConfirm: function handleConfirm() {\n      var _this3 = this;\n      this.$refs.changeForm.validate(function (valid) {\n        if (valid) {\n          // 显示再次确认弹窗\n          _this3.confirmDialogVisible = true;\n        }\n      });\n    },\n    handleFinalConfirm: function handleFinalConfirm() {\n      var data = {\n        terminalId: this.form.terminalId,\n        tenantId: this.form.tenantId\n      };\n      this.confirmDialogVisible = false;\n      this.$emit('confirm', data);\n    },\n    handleCancel: function handleCancel() {\n      this.drawerVisible = false;\n    },\n    handleClose: function handleClose() {\n      this.form = {\n        terminalId: '',\n        terminalName: '',\n        currentTenant: '',\n        tenantId: ''\n      };\n      this.confirmDialogVisible = false;\n      if (this.$refs.changeForm) {\n        this.$refs.changeForm.clearValidate();\n      }\n      this.$emit('close');\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "terminalInfo", "Object", "tenantList", "Array", "loading", "data", "form", "terminalId", "terminalName", "currentTenant", "tenantId", "rules", "required", "message", "trigger", "confirmDialogVisible", "computed", "drawerVisible", "get", "set", "val", "$emit", "availableTenants", "_this", "filter", "tenant", "id", "watch", "initForm", "handler", "deep", "methods", "_this2", "$nextTick", "$refs", "changeForm", "clearValidate", "handleConfirm", "_this3", "validate", "valid", "handleFinalConfirm", "handleCancel", "handleClose"], "sources": ["src/bysc_system/views/terminalAssignment/components/ChangeTenantDialog.vue"], "sourcesContent": ["<template>\n  <el-drawer\n    title=\"更改租户\"\n    :visible.sync=\"drawerVisible\"\n    direction=\"rtl\"\n    size=\"500px\"\n    :close-on-press-escape=\"false\"\n    :modal-append-to-body=\"false\"\n    @close=\"handleClose\"\n  >\n    <el-form :model=\"form\" :rules=\"rules\" ref=\"changeForm\" label-width=\"100px\">\n      <el-form-item label=\"终端ID\">\n        <el-input v-model=\"form.terminalId\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"终端名称\">\n        <el-input v-model=\"form.terminalName\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"当前租户\">\n        <el-input v-model=\"form.currentTenant\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"选择租户\" prop=\"tenantId\">\n        <el-select \n          v-model=\"form.tenantId\" \n          placeholder=\"支持搜索，不能选择当前租户\" \n          style=\"width: 100%\"\n          filterable\n        >\n          <el-option\n            v-for=\"tenant in availableTenants\"\n            :key=\"tenant.id\"\n            :label=\"tenant.tenantName\"\n            :value=\"tenant.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      \n      <!-- 注意提示 -->\n      <el-alert\n        title=\"注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！\"\n        type=\"warning\"\n        :closable=\"false\"\n        show-icon\n        style=\"margin-bottom: 20px;\">\n      </el-alert>\n    </el-form>\n    \n    <div class=\"drawer-footer\">\n      <el-button @click=\"handleCancel\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\" :loading=\"loading\">确 定</el-button>\n    </div>\n\n    <!-- 再次确认弹窗 -->\n    <el-dialog\n      title=\"再次确认更改\"\n      :visible.sync=\"confirmDialogVisible\"\n      width=\"400px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <div style=\"text-align: center; padding: 20px 0;\">\n        <p style=\"font-size: 16px; color: #E6A23C; margin-bottom: 20px;\">\n          请再次确认是否要进行租户绑定\n        </p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"confirmDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleFinalConfirm\" :loading=\"loading\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </el-drawer>\n</template>\n\n<script>\nexport default {\n  name: 'ChangeTenantDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    terminalInfo: {\n      type: Object,\n      default: () => ({})\n    },\n    tenantList: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      form: {\n        terminalId: '',\n        terminalName: '',\n        currentTenant: '',\n        tenantId: ''\n      },\n      rules: {\n        tenantId: [\n          { required: true, message: '请选择租户', trigger: 'change' }\n        ]\n      },\n      confirmDialogVisible: false\n    };\n  },\n  computed: {\n    drawerVisible: {\n      get() {\n        return this.visible;\n      },\n      set(val) {\n        this.$emit('update:visible', val);\n      }\n    },\n    // 过滤掉当前租户，不能选择当前租户\n    availableTenants() {\n      return this.tenantList.filter(tenant => \n        tenant.id !== this.terminalInfo.tenantId\n      );\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initForm();\n      } else {\n        this.confirmDialogVisible = false;\n      }\n    },\n    terminalInfo: {\n      handler(val) {\n        if (val && this.visible) {\n          this.initForm();\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initForm() {\n      this.form = {\n        terminalId: this.terminalInfo.terminalId || '',\n        terminalName: this.terminalInfo.terminalName || '',\n        currentTenant: this.terminalInfo.currentTenant || '',\n        tenantId: ''\n      };\n      // 清除验证\n      this.$nextTick(() => {\n        if (this.$refs.changeForm) {\n          this.$refs.changeForm.clearValidate();\n        }\n      });\n    },\n    \n    handleConfirm() {\n      this.$refs.changeForm.validate((valid) => {\n        if (valid) {\n          // 显示再次确认弹窗\n          this.confirmDialogVisible = true;\n        }\n      });\n    },\n    \n    handleFinalConfirm() {\n      const data = {\n        terminalId: this.form.terminalId,\n        tenantId: this.form.tenantId\n      };\n      this.confirmDialogVisible = false;\n      this.$emit('confirm', data);\n    },\n    \n    handleCancel() {\n      this.drawerVisible = false;\n    },\n    \n    handleClose() {\n      this.form = {\n        terminalId: '',\n        terminalName: '',\n        currentTenant: '',\n        tenantId: ''\n      };\n      this.confirmDialogVisible = false;\n      if (this.$refs.changeForm) {\n        this.$refs.changeForm.clearValidate();\n      }\n      this.$emit('close');\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.drawer-footer {\n  text-align: right;\n  padding: 20px;\n  border-top: 1px solid #e8e8e8;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n\n::v-deep .el-drawer__body {\n  padding-bottom: 80px;\n}\n\n::v-deep .el-alert__content {\n  line-height: 1.5;\n}\n</style>\n"], "mappings": "AAyEA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAK,OAAA;MACAP,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACAC,KAAA;QACAD,QAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,oBAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAtB,OAAA;MACA;MACAuB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,mBAAAD,GAAA;MACA;IACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA,YAAArB,UAAA,CAAAsB,MAAA,WAAAC,MAAA;QAAA,OACAA,MAAA,CAAAC,EAAA,KAAAH,KAAA,CAAAvB,YAAA,CAAAU,QAAA;MAAA,CACA;IACA;EACA;EACAiB,KAAA;IACA/B,OAAA,WAAAA,QAAAwB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAQ,QAAA;MACA;QACA,KAAAb,oBAAA;MACA;IACA;IACAf,YAAA;MACA6B,OAAA,WAAAA,QAAAT,GAAA;QACA,IAAAA,GAAA,SAAAxB,OAAA;UACA,KAAAgC,QAAA;QACA;MACA;MACAE,IAAA;IACA;EACA;EACAC,OAAA;IACAH,QAAA,WAAAA,SAAA;MAAA,IAAAI,MAAA;MACA,KAAA1B,IAAA;QACAC,UAAA,OAAAP,YAAA,CAAAO,UAAA;QACAC,YAAA,OAAAR,YAAA,CAAAQ,YAAA;QACAC,aAAA,OAAAT,YAAA,CAAAS,aAAA;QACAC,QAAA;MACA;MACA;MACA,KAAAuB,SAAA;QACA,IAAAD,MAAA,CAAAE,KAAA,CAAAC,UAAA;UACAH,MAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAC,UAAA,CAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAF,MAAA,CAAAvB,oBAAA;QACA;MACA;IACA;IAEA0B,kBAAA,WAAAA,mBAAA;MACA,IAAApC,IAAA;QACAE,UAAA,OAAAD,IAAA,CAAAC,UAAA;QACAG,QAAA,OAAAJ,IAAA,CAAAI;MACA;MACA,KAAAK,oBAAA;MACA,KAAAM,KAAA,YAAAhB,IAAA;IACA;IAEAqC,YAAA,WAAAA,aAAA;MACA,KAAAzB,aAAA;IACA;IAEA0B,WAAA,WAAAA,YAAA;MACA,KAAArC,IAAA;QACAC,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACA,KAAAK,oBAAA;MACA,SAAAmB,KAAA,CAAAC,UAAA;QACA,KAAAD,KAAA,CAAAC,UAAA,CAAAC,aAAA;MACA;MACA,KAAAf,KAAA;IACA;EACA;AACA", "ignoreList": []}]}